import { getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getPaymentProvider } from "@repo/payments";
import { Prisma } from "@prisma/client";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../../middleware/auth";
import { twoFactorMiddleware } from "../../../middleware/two-factor";

// Importante: Usamos basePath para evitar conflitos com outras rotas
export const transfersRouter = new Hono()
  // Não precisamos definir basePath aqui, pois já está definido no paymentsRouter
  .use("/*", authMiddleware);

// Create a new PIX transfer
transfersRouter.post(
  "/pix",
  authMiddleware as any, // Cast to any to bypass type issues temporarily
  async (c, next) => {
    // Check if using API key authentication - if yes, skip 2FA
    const apiKey = c.get("apiKey");
    if (apiKey) {
      // API key access doesn't require 2FA
      return await next();
    }

    // Not using API key, so we need to enforce 2FA
    await twoFactorMiddleware(c, next);
  },
  validator("json", z.object({
    amount: z.number().positive(),
    pixKey: z.string(),
    pixKeyType: z.enum(["CPF", "CNPJ", "EMAIL", "PHONE", "RANDOM"]),
    description: z.string().optional(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Create a new PIX transfer",
    description: "Creates a new PIX transfer to a PIX key",
    responses: {
      201: {
        description: "Transfer created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    // Declarar variáveis no escopo da função para acesso em todos os blocos
    let transaction;
    let gatewayFee = 0; // Inicializar a taxa do gateway
    const {
      amount,
      pixKey,
      pixKeyType,
      description,
      organizationId
    } = c.req.valid("json");
    const session = c.get("session");

    try {

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
        // API key is authorized for this organization - continue with the transfer
        logger.info("Processing PIX transfer via API key", {
          organizationId,
          apiKeyId: apiKey.id
        });
      } else {
        // Verify organization membership for regular users
        const membership = await getOrganizationMembership(session.userId, organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this organization" });
        }
      }

      // Validate the key format based on type
      const validateKey = (key: string, type: string): boolean => {
        switch (type) {
          case "CPF":
            return /^\d{11}$/.test(key);
          case "CNPJ":
            return /^\d{14}$/.test(key);
          case "EMAIL":
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(key);
          case "PHONE":
            return /^\+\d{1,3}\d{8,14}$/.test(key);
          case "RANDOM":
            return /^[a-zA-Z0-9]{32,36}$/.test(key);
          default:
            return false;
        }
      };

      if (!validateKey(pixKey, pixKeyType)) {
        throw new HTTPException(400, { message: `Invalid format for Pix key type: ${pixKeyType}` });
      }

      // Verificar se a organização tem saldo suficiente
      const balance = await db.organizationBalance.findUnique({
        where: { organizationId }
      });

      if (!balance) {
        logger.warn(`Organização sem registro de saldo: ${organizationId}`);
        throw new HTTPException(400, { message: "Insufficient balance for this transfer" });
      }

      // Buscar o gateway para obter a taxa de transferência
      const gateway = await db.paymentGateway.findFirst({
        where: {
          isActive: true,
          canSend: true,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });

      // Se não encontrou gateway, buscar um global
      const gatewayToUse = gateway || await db.paymentGateway.findFirst({
        where: {
          isActive: true,
          canSend: true,
          isGlobal: true
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });

      if (!gatewayToUse) {
        throw new HTTPException(400, { message: "No payment gateway available for transfers" });
      }

      // Calcular o valor total com a taxa
      gatewayFee = gatewayToUse.pixTransferFixedFee || 0;
      const totalAmount = amount + gatewayFee;

      // Verificar se há saldo suficiente (incluindo a taxa)
      if (balance.availableBalance < totalAmount) {
        logger.warn(`Saldo insuficiente para transferência: ${balance.availableBalance.toFixed(2)} < ${totalAmount.toFixed(2)}`, {
          organizationId,
          available: Number(balance.availableBalance.toFixed(2)),
          requested: Number(amount.toFixed(2)),
          fee: Number(gatewayFee.toFixed(2)),
          totalRequired: Number(totalAmount.toFixed(2))
        });

        throw new HTTPException(400, {
          message: `Insufficient balance for this transfer. Available: ${balance.availableBalance.toFixed(2)}, Requested: ${amount.toFixed(2)}, Fee: ${gatewayFee.toFixed(2)}, Total required: ${totalAmount.toFixed(2)}`
        });
      }

      // Create a reference code for this transaction
      const referenceCode = `tx_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Create the transaction in the database first
      transaction = await db.transaction.create({
        data: {
          customerName: "PIX Transfer",
          customerEmail: "<EMAIL>",
          amount,
          status: "PROCESSING",
          type: "SEND",
          description: description || "PIX Transfer",
          pixKey,
          pixKeyType,
          referenceCode,
          organizationId,
          gatewayId: gatewayToUse.id,
          gatewayName: gatewayToUse.name,
          // Store fee information in transaction record
          percentFee: 0, // PIX transfers typically have fixed fees only
          fixedFee: gatewayFee,
          totalFee: gatewayFee,
          netAmount: amount, // For transfers, netAmount is the transfer amount (fees are separate)
          metadata: {
            source: apiKey ? "api_key" : "web_interface",
            transferInitiatedAt: new Date().toISOString(),
            twoFactorVerified: !!apiKey, // API keys don't need 2FA
            fee: gatewayFee,
            totalAmount: totalAmount,
            gatewayType: gatewayToUse.type,
            feeCalculation: {
              percentFee: 0,
              fixedFee: gatewayFee,
              totalFee: gatewayFee,
              transferAmount: amount,
              totalReserved: totalAmount
            }
          }
        },
      });

      // Reservar o valor total (incluindo a taxa) no saldo da organização
      await db.organizationBalance.update({
        where: { organizationId },
        data: {
          availableBalance: { decrement: totalAmount },
          reservedBalance: { increment: totalAmount }
        }
      });

      // Registrar a operação no histórico de saldo
      await db.balanceHistory.create({
        data: {
          organizationId,
          transactionId: transaction.id,
          operation: "RESERVE",
          amount: totalAmount,
          description: `Reserva para transferência PIX: ${transaction.id} (valor: ${amount}, taxa: ${gatewayFee})`,
          balanceAfterOperation: {
            available: Number((balance.availableBalance - totalAmount).toFixed(2)),
            pending: Number(balance.pendingBalance.toFixed(2)),
            reserved: Number((balance.reservedBalance + totalAmount).toFixed(2))
          },
          balanceId: balance.id
        }
      });

      // Disparar eventos de webhook para a transação criada
      try {
        const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
        await triggerTransactionEvents(transaction);
        logger.info("Webhook events triggered for transfer transaction", { transactionId: transaction.id });
      } catch (webhookError) {
        logger.error("Error triggering webhook events for transfer", {
          error: webhookError,
          transactionId: transaction.id
        });
        // Não interromper o fluxo se houver erro nos webhooks
      }

      // Get the payment provider that supports sending PIX
      // No preferred gateway type specified as requested
      const paymentProvider = await getPaymentProvider(organizationId, {
        action: 'withdrawal' // This will ensure we select a gateway with canSend=true
      });

      // Create the postback URL
      const postbackUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/transfers`;

      // Process the PIX transfer
      try {
        logger.info("Attempting to call paymentProvider.processPixWithdrawal", { amount, pixKey, pixKeyType, organizationId });
        const withdrawalStartTime = Date.now();
        const transferResult = await paymentProvider.processPixWithdrawal({
          amount,
          pixKey,
          pixKeyType,
          postbackUrl,
          organizationId,
        });
        logger.info("paymentProvider.processPixWithdrawal call finished", { duration: Date.now() - withdrawalStartTime, transferResult });

        // Determine the primary external ID to use
        // For Pluggou PIX, we prioritize idEnvio/flow2pay_id as the primary identifier
        const primaryExternalId = transferResult.idEnvio ||
                                 transferResult.id_envio ||
                                 transferResult.externalId ||
                                 transferResult.flow2pay_id ||
                                 transferResult.id;

        // Log the identifiers for debugging
        logger.info("Updating transaction with external identifiers", {
          transactionId: transaction.id,
          primaryExternalId,
          providerId: transferResult.id,
          idEnvio: transferResult.idEnvio || transferResult.id_envio,
          id_envio: transferResult.id_envio,
          flow2pay_id: transferResult.flow2pay_id,
          gatewayType: gatewayToUse.type
        });

        // Update the transaction with all identifiers from the transfer result
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            // Always use the primary external ID in the externalId field
            externalId: primaryExternalId,
            // Directly set the referenceCode to the id_envio for better matching
            referenceCode: transferResult.id_envio || transferResult.idEnvio || transferResult.externalId,
            // Store the endToEndId if available (will be filled by webhook later)
            ...(transferResult.endToEndId && { endToEndId: transferResult.endToEndId }),
            ...(transferResult.gatewayId && { gatewayId: transferResult.gatewayId }),
            metadata: {
              ...(transaction.metadata as Record<string, any> || {}),
              // Store all possible identifiers for webhook matching
              externalId: primaryExternalId, // Store in metadata as well for consistency
              id_envio: transferResult.id_envio || transferResult.idEnvio,
              idEnvio: transferResult.idEnvio || transferResult.id_envio,
              flow2pay_id: transferResult.flow2pay_id || primaryExternalId,
              txid: transferResult.txid || primaryExternalId,
              transactionCode: transferResult.transactionId || transferResult.codigoTransacao || transferResult.id,
              codigoTransacao: transferResult.codigoTransacao || transferResult.transactionId || transferResult.id,

              // Store a comprehensive allIdentifiers object for robust matching
              allIdentifiers: {
                ...((transaction.metadata as any)?.allIdentifiers || {}),
                id_envio: transferResult.id_envio || transferResult.idEnvio,
                idEnvio: transferResult.idEnvio || transferResult.id_envio,
                txid: transferResult.txid || primaryExternalId,
                externalId: primaryExternalId
              },

              // Store provider-specific data
              pluggou: {
                ...((transaction.metadata as any)?.pluggou || {}),
                txid: transferResult.txid,
                id: transferResult.id,
                id_envio: transferResult.id_envio || transferResult.idEnvio,
                idEnvio: transferResult.idEnvio || transferResult.id_envio,
                flow2pay_id: transferResult.flow2pay_id
              },

              // Store the raw response for debugging
              transferInitiationResponse: transferResult.raw || transferResult,
              transferInitiatedAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),

              // Store type information to help webhook handlers
              type: "PIX_OUT",
              provider: gatewayToUse.type
            }
          },
        });
      } catch (processingError) {
        logger.error("paymentProvider.processPixWithdrawal call failed", { duration: Date.now() - withdrawalStartTime, error: processingError });
        logger.error("Erro ao processar transferência PIX", {
          error: processingError,
          transactionId: transaction.id,
          organizationId
        });

        // Atualizar o status da transação para CANCELED
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            status: "CANCELED",
            metadata: {
              ...transaction.metadata as any,
              error: processingError instanceof Error ? processingError.message : "Erro desconhecido",
              errorAt: new Date().toISOString()
            }
          }
        });

        // Devolver o saldo reservado (incluindo a taxa)
        const updatedBalance = await db.organizationBalance.update({
          where: { organizationId },
          data: {
            availableBalance: { increment: totalAmount },
            reservedBalance: { decrement: totalAmount }
          }
        });

        // Registrar a operação no histórico de saldo
        await db.balanceHistory.create({
          data: {
            organizationId,
            transactionId: transaction.id,
            operation: "UNRESERVE",
            amount: totalAmount,
            description: `Devolução de reserva por falha na transferência: ${transaction.id} (incluindo taxa)`,
            balanceAfterOperation: {
              available: Number(updatedBalance.availableBalance.toFixed(2)),
              pending: Number(updatedBalance.pendingBalance.toFixed(2)),
              reserved: Number(updatedBalance.reservedBalance.toFixed(2))
            },
            balanceId: updatedBalance.id
          }
        });

        throw new HTTPException(500, {
          message: `${processingError instanceof Error ? processingError.message : "Falha ao processar transferência PIX"}. Transaction ID: ${transaction.id}`
        });
      }

      // Buscar a transação atualizada para garantir que temos os dados mais recentes
      const updatedTransaction = await db.transaction.findUnique({
        where: { id: transaction.id }
      });

      return c.json({
        id: transaction.id,
        referenceCode,
        status: updatedTransaction?.status || transaction.status,
        amount: Number(amount.toFixed(2)),
        pixKey,
        pixKeyType,
        description,
        externalId: updatedTransaction?.externalId,
        fee: Number(gatewayFee.toFixed(2)),
        totalAmount: Number(totalAmount.toFixed(2)),
        gatewayType: gatewayToUse.type
      }, 201);
    } catch (error) {
      logger.error("Error creating PIX transfer", { error });

      // Se já é uma HTTPException, pode ser o erro que lançamos no bloco try/catch interno
      if (error instanceof HTTPException) {
        throw error;
      }

      // Se chegou até aqui, é um erro não tratado anteriormente
      // Verificar se a transação foi criada e se precisamos reverter o saldo
      if (transaction?.id) {
        try {
          // Verificar se a transação já está cancelada
          const existingTransaction = await db.transaction.findUnique({
            where: { id: transaction.id }
          });

          if (existingTransaction && existingTransaction.status !== "CANCELED") {
            // Atualizar o status da transação para CANCELED
            await db.transaction.update({
              where: { id: transaction.id },
              data: {
                status: "CANCELED",
                metadata: {
                  ...(existingTransaction.metadata as any || {}),
                  error: error instanceof Error ? error.message : "Erro desconhecido no catch externo",
                  errorAt: new Date().toISOString()
                }
              }
            });

            // Devolver o saldo reservado (incluindo a taxa)
            const totalToRestore = amount + gatewayFee;
            const updatedBalance = await db.organizationBalance.update({
              where: { organizationId },
              data: {
                availableBalance: { increment: totalToRestore },
                reservedBalance: { decrement: totalToRestore }
              }
            });

            // Registrar a operação no histórico de saldo
            await db.balanceHistory.create({
              data: {
                organizationId, // Use organizationId from the outer scope
                transactionId: transaction.id,
                operation: "UNRESERVE",
                amount: totalToRestore,
                description: `Devolução de reserva por erro externo: ${transaction.id} (valor: ${totalToRestore})`,
                balanceAfterOperation: {
                  available: Number(updatedBalance.availableBalance.toFixed(2)),
                  pending: Number(updatedBalance.pendingBalance.toFixed(2)),
                  reserved: Number(updatedBalance.reservedBalance.toFixed(2))
                },
                balanceId: updatedBalance.id
              }
            });

            logger.info("Saldo revertido com sucesso no catch externo", { transactionId: transaction.id, organizationId, amountReverted: totalToRestore });
          } else {
            logger.info("Transação já estava cancelada ou não encontrada no catch externo", { transactionId: transaction.id, organizationId });
          }
        } catch (reversalError) {
          logger.error("Erro ao tentar reverter saldo no catch externo", {
            reversalError,
            originalError: error,
            transactionId: transaction?.id,
            organizationId
          });
        }
      }

      // Lançar um erro genérico para o cliente
      throw new HTTPException(500, { message: `Failed to process PIX transfer. Please contact support. Transaction ID (if available): ${transaction?.id || 'N/A'}` });
    }
  }
);



// List transfers
transfersRouter.get(
  "/",
  validator("query", z.object({
    organizationId: z.string(),
    status: z.string().optional(),
    search: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("10"),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "List transfers",
    description: "Lists transfers with optional filters",
    responses: {
      200: {
        description: "Transfers retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const {
        organizationId,
        status,
        search,
        startDate,
        endDate,
        page,
        limit
      } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Build the query
      const where: Prisma.TransactionWhereInput = {
        organizationId,
        type: "SEND", // Only include transfers
      };

      if (status && status !== "all") {
        where.status = status.toUpperCase() as any;
      }

      if (search) {
        where.OR = [
          { id: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { externalId: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { referenceCode: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { customerName: { contains: search, mode: Prisma.QueryMode.insensitive } },
          { pixKey: { contains: search, mode: Prisma.QueryMode.insensitive } },
        ];
      }

      if (startDate || endDate) {
        where.createdAt = {} as Prisma.DateTimeFilter;

        if (startDate) {
          (where.createdAt as Prisma.DateTimeFilter).gte = new Date(startDate);
        }

        if (endDate) {
          (where.createdAt as Prisma.DateTimeFilter).lte = new Date(endDate);
        }
      }

      // Get total count
      const total = await db.transaction.count({ where });

      // Get transfers (gateway information removed as requested)
      const transfers = await db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return c.json({
        data: transfers.map(tx => ({
          id: tx.id,
          externalId: tx.externalId,
          referenceCode: tx.referenceCode,
          amount: tx.amount,
          status: tx.status,
          description: tx.description,
          pixKey: tx.pixKey,
          pixKeyType: tx.pixKeyType,
          date: tx.createdAt.toISOString(),
          paymentDate: tx.paymentAt?.toISOString(),
          recipient: {
            name: tx.customerName,
            pixKey: tx.pixKey,
            pixKeyType: tx.pixKeyType,
          },
          // Gateway information removed as requested
        })),
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      logger.error("Error listing transfers", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list transfers" });
    }
  }
);

// Get transfers summary
transfersRouter.get(
  "/summary",
  validator("query", z.object({
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Get transfers summary",
    description: "Gets summary statistics for transfers",
    responses: {
      200: {
        description: "Summary retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { organizationId } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Obter total de transferências enviadas (SEND)
      const outgoingTransfers = await db.transaction.findMany({
        where: {
          organizationId,
          type: "SEND",
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      // Obter total de transferências recebidas (RECEIVE)
      const incomingTransfers = await db.transaction.findMany({
        where: {
          organizationId,
          type: "RECEIVE",
        },
        select: {
          amount: true,
          createdAt: true,
        },
      });

      // Calcular volume total
      const totalVolume = [...outgoingTransfers, ...incomingTransfers].reduce(
        (sum, tx) => sum + tx.amount,
        0
      );

      // Calcular valor médio
      const averageValue = totalVolume / (outgoingTransfers.length + incomingTransfers.length || 1);

      // Calcular crescimento (mock - em uma implementação real, compararia com período anterior)
      // Aqui estamos usando valores fixos para demonstração
      const totalVolumeGrowth = 12.5;
      const incomingTransfersGrowth = 8.3;
      const outgoingTransfersGrowth = -4.5;
      const averageValueGrowth = 7.2;

      // Montar a resposta
      const data = {
        totalVolume: {
          amount: totalVolume,
          growth: totalVolumeGrowth,
        },
        incomingTransfers: {
          count: incomingTransfers.length,
          growth: incomingTransfersGrowth,
        },
        outgoingTransfers: {
          count: outgoingTransfers.length,
          growth: outgoingTransfersGrowth,
        },
        averageValue: {
          amount: averageValue,
          growth: averageValueGrowth,
        },
      };

      return c.json(data);
    } catch (error) {
      logger.error("Error getting transfers summary", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transfers summary" });
    }
  }
);

// Sync transfer status with gateway
transfersRouter.post(
  "/sync-status",
  validator("json", z.object({
    transactionId: z.string(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Sync transfer status with gateway",
    description: "Manually syncs a transfer's status with the payment gateway",
    responses: {
      200: {
        description: "Transfer status synced successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transfer not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { transactionId, organizationId } = c.req.valid("json");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Find the transaction
      const transaction = await db.transaction.findUnique({
        where: { id: transactionId },
        include: {
          gateway: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transfer not found" });
      }

      // Verify the transaction belongs to the organization
      if (transaction.organizationId !== organizationId) {
        throw new HTTPException(403, { message: "You don't have access to this transfer" });
      }

      // Get the payment provider that processed this transaction
      const provider = await getPaymentProvider(organizationId, {
        forceType: transaction.gateway?.type,
        action: 'status'
      });

      if (!provider) {
        throw new HTTPException(400, { message: "Payment provider not found" });
      }

      // Check the status with the gateway
      const statusResult = await provider.getTransactionStatus({
        transactionId: transaction.externalId || transaction.id,
        organizationId: transaction.organizationId,
      });

      // Map the status
      let mappedStatus = transaction.status;
      if (statusResult.mappedStatus) {
        // If the provider returns a mapped status, use it directly
        mappedStatus = statusResult.mappedStatus;
      } else if (statusResult.status) {
        // Fallback mapping
        const statusMap: Record<string, string> = {
          "PENDING": "PENDING",
          "APPROVED": "APPROVED",
          "CONFIRMED": "APPROVED",
          "RECEIVED": "APPROVED",
          "REJECTED": "REJECTED",
          "CANCELED": "CANCELED",
          "PROCESSING": "PROCESSING",
          "REFUNDED": "REFUNDED",
        };
        mappedStatus = (statusMap[statusResult.status.toUpperCase()] || transaction.status) as any;
      }

      // Update the transaction if the status has changed
      let updatedTransaction = transaction;
      if (mappedStatus !== transaction.status) {
        const isCompleted = mappedStatus === "APPROVED" ||
                          mappedStatus === "REFUNDED";

        updatedTransaction = await db.transaction.update({
          where: { id: transactionId },
          data: {
            status: mappedStatus,
            paymentAt: isCompleted ? new Date() : transaction.paymentAt,
          },
          include: {
            gateway: true,
          },
        });
      }

      return c.json({
        success: true,
        transfer: {
          id: updatedTransaction.id,
          externalId: updatedTransaction.externalId,
          status: updatedTransaction.status,
          amount: updatedTransaction.amount,
          pixKey: updatedTransaction.pixKey,
          pixKeyType: updatedTransaction.pixKeyType,
          date: updatedTransaction.createdAt.toISOString(),
          paymentDate: updatedTransaction.paymentAt?.toISOString(),
          updatedAt: updatedTransaction.updatedAt.toISOString(),
          description: updatedTransaction.description,
          recipient: {
            name: updatedTransaction.customerName,
            pixKey: updatedTransaction.pixKey,
            pixKeyType: updatedTransaction.pixKeyType,
          },
          gateway: updatedTransaction.gateway ? {
            name: updatedTransaction.gateway.name,
            type: updatedTransaction.gateway.type,
          } : null,
        },
        previousStatus: transaction.status,
        newStatus: updatedTransaction.status,
        message: mappedStatus !== transaction.status
          ? "Transfer status updated successfully"
          : "Transfer status is already up to date",
      });
    } catch (error) {
      logger.error("Error syncing transfer status", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to sync transfer status" });
    }
  }
);

// Get transfer by ID
transfersRouter.get(
  "/:id",
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transfers"],
    summary: "Get transfer by ID",
    description: "Retrieves a transfer by its ID",
    responses: {
      200: {
        description: "Transfer retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transfer not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      const transaction = await db.transaction.findUnique({
        where: { id },
        include: {
          gateway: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transfer not found" });
      }

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this transfer" });
      }

      // If the transaction is in a processing state, check its status with the gateway
      if (transaction.status === "PROCESSING" && transaction.externalId) {
        try {
          const provider = await getPaymentProvider(transaction.organizationId);
          const statusResult = await provider.getTransactionStatus({
            transactionId: transaction.externalId,
            organizationId: transaction.organizationId,
          });

          // Map the status
          let mappedStatus = transaction.status;
          if (statusResult.mappedStatus) {
            // If the provider returns a mapped status, use it directly
            mappedStatus = statusResult.mappedStatus;
          } else if (statusResult.status) {
            // Fallback mapping
            const statusMap: Record<string, string> = {
              "PENDING": "PENDING",
              "APPROVED": "APPROVED",
              "CONFIRMED": "APPROVED",
              "RECEIVED": "APPROVED",
              "REJECTED": "REJECTED",
              "CANCELED": "CANCELED",
              "PROCESSING": "PROCESSING",
              "REFUNDED": "REFUNDED",
            };
            mappedStatus = (statusMap[statusResult.status.toUpperCase()] || transaction.status) as any;
          }

          // Update the transaction if the status has changed
          if (mappedStatus !== transaction.status) {
            const isCompleted = mappedStatus === "APPROVED" ||
                              mappedStatus === "REFUNDED";

            await db.transaction.update({
              where: { id },
              data: {
                status: mappedStatus,
                paymentAt: isCompleted ? new Date() : transaction.paymentAt,
              },
            });

            // Refresh the transaction data
            transaction.status = mappedStatus;
            if (isCompleted) {
              transaction.paymentAt = new Date();
            }
          }
        } catch (error) {
          logger.error("Error checking transfer status", { error, transactionId: id });
          // Continue with the current transaction data
        }
      }

      return c.json({
        id: transaction.id,
        externalId: transaction.externalId,
        referenceCode: transaction.referenceCode,
        amount: transaction.amount,
        status: transaction.status,
        type: transaction.type,
        description: transaction.description,
        pixKey: transaction.pixKey,
        pixKeyType: transaction.pixKeyType,
        createdAt: transaction.createdAt,
        paymentAt: transaction.paymentAt,
        updatedAt: transaction.updatedAt,
        gateway: transaction.gateway ? {
          id: transaction.gateway.id,
          name: transaction.gateway.name,
          type: transaction.gateway.type,
        } : null,
      });
    } catch (error) {
      logger.error("Error getting transfer", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transfer" });
    }
  }
);
