import { Transaction, TransactionStatus } from "@prisma/client";
import { createWebhookEvent } from "./service";
import { logger } from "@repo/logs";

/**
 * Event types for webhooks
 */
export enum WebhookEventType {
  // Transaction events
  TRANSACTION_CREATED = "transaction.created",
  TRANSACTION_UPDATED = "transaction.updated",
  TRANSACTION_PAID = "transaction.paid",
  TRANSACTION_FAILED = "transaction.failed",
  TRANSACTION_REFUNDED = "transaction.refunded",
  TRANSACTION_CANCELED = "transaction.canceled",

  // Refund events
  REFUND_CREATED = "refund.created",
  REFUND_UPDATED = "refund.updated",

  // PIX specific events
  PIX_IN_PROCESSING = "pix.in.processing",
  PIX_IN_CONFIRMATION = "pix.in.confirmation",
  PIX_OUT_PROCESSING = "pix.out.processing",
  PIX_OUT_CONFIRMATION = "pix.out.confirmation",
  PIX_OUT_FAILURE = "pix.out.failure",
  PIX_IN_REVERSAL_PROCESSING = "pix.in.reversal.processing",
  PIX_IN_REVERSAL_CONFIRMATION = "pix.in.reversal.confirmation",
  PIX_OUT_REVERSAL = "pix.out.reversal"
}

/**
 * Create webhook events when transaction status changes
 */
export async function triggerTransactionEvents(
  transaction: Transaction,
  previousStatus?: TransactionStatus
) {
  try {
    logger.info("Iniciando disparo de eventos de webhook para transação", {
      transactionId: transaction.id,
      status: transaction.status,
      previousStatus: previousStatus || "NONE",
      type: transaction.type
    });

    // Se não há status anterior, é uma nova transação
    if (!previousStatus) {
      // Criar evento de transação criada
      logger.info("Disparando evento de transação criada", {
        transactionId: transaction.id,
        eventType: WebhookEventType.TRANSACTION_CREATED
      });

      await createWebhookEvent({
        type: WebhookEventType.TRANSACTION_CREATED,
        payload: {
          id: transaction.id,
          externalId: transaction.externalId,
          referenceCode: transaction.referenceCode,
          endToEndId: transaction.endToEndId,
          status: transaction.status,
          type: transaction.type,
          amount: transaction.amount,
          customerName: transaction.customerName,
          customerEmail: transaction.customerEmail,
          customerDocument: transaction.customerDocument,
          createdAt: transaction.createdAt,
          paymentAt: transaction.paymentAt,
          organizationId: transaction.organizationId,
          // PIX-specific fields
          pixKey: transaction.pixKey,
          pixKeyType: transaction.pixKeyType,
          description: transaction.description,
          // Fee information
          percentFee: transaction.percentFee,
          fixedFee: transaction.fixedFee,
          totalFee: transaction.totalFee,
          netAmount: transaction.netAmount,
        },
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
      });

      logger.info("Created transaction.created webhook event", {
        transactionId: transaction.id,
        status: transaction.status,
      });

      return;
    }

    // Se o status não mudou, não dispara evento de atualização
    if (previousStatus === transaction.status) {
      return;
    }

    // Determine the event type based on transaction type, status, and previous status
    let eventType: string;
    let pixEventType: string | null = null;

    // First, determine PIX-specific event types based on transaction type and status
    // Check if this is a PIX transaction by examining metadata or PIX-specific fields
    const metadata = transaction.metadata as Record<string, any> || {};
    const isPix = metadata.providerType === "PIX" ||
                  metadata.provider === "PLUGGOU_PIX" ||
                  transaction.pixKey !== undefined ||
                  transaction.pixKeyType !== undefined;

    logger.info("Analyzing transaction for PIX event type", {
      transactionId: transaction.id,
      type: transaction.type,
      status: transaction.status,
      isPix,
      hasPixKey: !!transaction.pixKey,
      hasPixKeyType: !!transaction.pixKeyType,
      metadataProvider: metadata.provider,
      metadataProviderType: metadata.providerType,
      metadataTransferType: metadata.transferType
    });

    if (transaction.type === "RECEIVE" || (isPix && transaction.type === "CHARGE")) {
      // PIX IN events
      switch (transaction.status) {
        case TransactionStatus.PROCESSING:
          pixEventType = WebhookEventType.PIX_IN_PROCESSING;
          break;
        case TransactionStatus.APPROVED:
          pixEventType = WebhookEventType.PIX_IN_CONFIRMATION;
          break;
        case TransactionStatus.REFUNDED:
          // Check if this is a reversal by looking at originalTransactionId
          if (transaction.originalTransactionId) {
            pixEventType = WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION;
          } else {
            pixEventType = WebhookEventType.PIX_IN_CONFIRMATION;
          }
          break;
      }
    } else if (transaction.type === "SEND" || (isPix && transaction.type === "CHARGE" && metadata.transferType === "withdrawal")) {
      // PIX OUT events
      switch (transaction.status) {
        case TransactionStatus.PROCESSING:
          pixEventType = WebhookEventType.PIX_OUT_PROCESSING;
          break;
        case TransactionStatus.APPROVED:
          pixEventType = WebhookEventType.PIX_OUT_CONFIRMATION;
          break;
        case TransactionStatus.REJECTED:
        case TransactionStatus.BLOCKED:
          pixEventType = WebhookEventType.PIX_OUT_FAILURE;
          break;
      }
    } else if (transaction.type === "REFUND") {
      // PIX REFUND/REVERSAL events
      if (transaction.originalTransactionId) {
        // This is a refund transaction
        switch (transaction.status) {
          case TransactionStatus.PROCESSING:
            pixEventType = WebhookEventType.PIX_IN_REVERSAL_PROCESSING;
            break;
          case TransactionStatus.APPROVED:
            pixEventType = WebhookEventType.PIX_OUT_REVERSAL;
            break;
        }
      }
    }

    // Use PIX-specific event type if available, otherwise fall back to generic transaction events
    if (pixEventType) {
      eventType = pixEventType;
      logger.info("Selected PIX-specific event type", {
        transactionId: transaction.id,
        eventType: pixEventType,
        transactionType: transaction.type,
        status: transaction.status,
        isPix
      });
    } else {
      // Generic transaction events
      switch (transaction.status) {
        case TransactionStatus.APPROVED:
          eventType = WebhookEventType.TRANSACTION_PAID;
          break;
        case TransactionStatus.REJECTED:
          eventType = WebhookEventType.TRANSACTION_FAILED;
          break;
        case TransactionStatus.CANCELED:
          eventType = WebhookEventType.TRANSACTION_CANCELED;
          break;
        case TransactionStatus.REFUNDED:
          eventType = WebhookEventType.TRANSACTION_REFUNDED;
          break;
        case TransactionStatus.BLOCKED:
          eventType = WebhookEventType.TRANSACTION_FAILED;
          break;
        default:
          eventType = WebhookEventType.TRANSACTION_UPDATED;
      }
      logger.info("Selected generic transaction event type", {
        transactionId: transaction.id,
        eventType,
        transactionType: transaction.type,
        status: transaction.status,
        isPix
      });
    }

    logger.info("Disparando evento de atualização de status da transação", {
      transactionId: transaction.id,
      eventType,
      previousStatus: previousStatus || "NONE",
      currentStatus: transaction.status
    });

    // Create enhanced webhook payload with PIX-specific information
    const webhookPayload = {
      id: transaction.id,
      externalId: transaction.externalId,
      referenceCode: transaction.referenceCode,
      endToEndId: transaction.endToEndId,
      status: transaction.status,
      previousStatus,
      type: transaction.type,
      amount: transaction.amount,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      customerDocument: transaction.customerDocument,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      paymentAt: transaction.paymentAt,
      organizationId: transaction.organizationId,
      // PIX-specific fields
      pixKey: transaction.pixKey,
      pixKeyType: transaction.pixKeyType,
      description: transaction.description,
      // Fee information
      percentFee: transaction.percentFee,
      fixedFee: transaction.fixedFee,
      totalFee: transaction.totalFee,
      netAmount: transaction.netAmount,
      // Refund information if applicable
      ...(transaction.originalTransactionId && {
        originalTransactionId: transaction.originalTransactionId,
        reason: transaction.reason,
      }),
    };

    // Create the webhook event
    await createWebhookEvent({
      type: eventType,
      payload: webhookPayload,
      transactionId: transaction.id,
      organizationId: transaction.organizationId,
    });

    logger.info("Created transaction status webhook event", {
      transactionId: transaction.id,
      eventType,
      status: transaction.status,
      previousStatus,
    });
  } catch (error) {
    logger.error("Error creating transaction webhook event", {
      error,
      transactionId: transaction.id,
      status: transaction.status,
    });
  }
}

/**
 * Triggers webhook events for a refund
 */
export async function triggerRefundEvents(refund: any, isNew: boolean = true) {
  try {
    const organizationId = refund.transaction.organizationId;
    const transactionId = refund.transactionId;

    // Trigger the appropriate event
    await createWebhookEvent({
      type: isNew
        ? WebhookEventType.REFUND_CREATED
        : WebhookEventType.REFUND_UPDATED,
      payload: refund,
      transactionId,
      organizationId,
    });
  } catch (error) {
    logger.error("Error triggering refund webhook events", {
      error,
      refundId: refund.id,
    });
  }
}
